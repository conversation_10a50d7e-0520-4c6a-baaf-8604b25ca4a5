import { createClient } from "@/supabase/client/server";

export default async function TestAuthPage() {
  const supabase = await createClient();
  
  // Test basic connection
  try {
    const { data, error } = await supabase.auth.getSession();
    
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Auth Test Page</h1>
        
        <div className="space-y-4">
          <div>
            <h2 className="text-lg font-semibold">Environment Variables:</h2>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}
              {'\n'}NEXT_PUBLIC_SUPABASE_ANON_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}
            </pre>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold">Session Status:</h2>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify({ 
                hasSession: !!data.session,
                error: error?.message || 'None',
                user: data.session?.user ? {
                  id: data.session.user.id,
                  email: data.session.user.email
                } : null
              }, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    );
  } catch (err) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Auth Test Page - Error</h1>
        <pre className="bg-red-100 p-2 rounded text-sm">
          {err instanceof Error ? err.message : 'Unknown error'}
        </pre>
      </div>
    );
  }
}
