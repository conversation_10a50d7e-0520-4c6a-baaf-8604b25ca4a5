import { createClient } from "@/supabase/client/server";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  // The `/auth/callback` route is required for the server-side auth flow implemented
  // by the SSR package. It exchanges an auth code for the user's session.
  // https://supabase.com/docs/guides/auth/server-side/nextjs
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const error = requestUrl.searchParams.get("error");
  const errorDescription = requestUrl.searchParams.get("error_description");

  console.log("AUTH CALLBACK DEBUG:", {
    url: requestUrl.toString(),
    code: !!code,
    error,
    errorDescription,
    searchParams: Object.fromEntries(requestUrl.searchParams.entries())
  });

  // Use the NEXT_PUBLIC_SITE_URL environment variable if available, otherwise fall back to request origin
  const origin = process.env.NEXT_PUBLIC_SITE_URL || requestUrl.origin;

  const redirectTo = requestUrl.searchParams.get("redirect_to")?.toString();

  // If there's an OAuth error, redirect to sign-in with error
  if (error) {
    console.error("OAuth error in callback:", { error, errorDescription });
    return NextResponse.redirect(`${origin}/sign-in?error=${encodeURIComponent(error)}&message=${encodeURIComponent(errorDescription || 'OAuth authentication failed')}`);
  }

  const supabase = await createClient();

  if (code) {
    console.log("Exchanging code for session...");
    const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

    if (exchangeError) {
      console.error("Error exchanging code for session:", exchangeError);
      return NextResponse.redirect(`${origin}/sign-in?error=exchange_failed&message=${encodeURIComponent(exchangeError.message)}`);
    }

    console.log("Code exchange successful:", {
      hasSession: !!data.session,
      hasUser: !!data.user,
      userId: data.user?.id
    });
  } else {
    console.log("No code provided in callback");
  }

  // Get the user after authentication
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError) {
    console.error("Error getting user after callback:", userError);
    return NextResponse.redirect(`${origin}/sign-in?error=user_fetch_failed&message=${encodeURIComponent(userError.message)}`);
  }

  // Log successful authentication
  if (user && user.email) {
    console.log(`AUTH CALLBACK: User ${user.email} successfully authenticated`);
  } else {
    console.log("AUTH CALLBACK: No user found after authentication");
  }

  if (redirectTo) {
    console.log(`Redirecting to: ${origin}${redirectTo}`);
    return NextResponse.redirect(`${origin}${redirectTo}`);
  }

  // URL to redirect to after sign in process completes
  console.log(`Redirecting to home: ${origin}/home`);
  return NextResponse.redirect(`${origin}/home`);
}
